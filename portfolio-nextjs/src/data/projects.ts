import { Project } from '@/lib/types';

export const projects: Project[] = [
  {
    id: 'saas-dashboard',
    title: 'Trade Book Ledge',
    description: 'SaaS business management platform for trade operations',
    longDescription: 'A comprehensive SaaS business management platform designed for trade operations. Features inventory management, financial tracking, customer relationship management, and automated reporting capabilities built with modern no-code tools.',
    technologies: ['Bubble.io', 'Supabase', 'Zapier', 'Chart.js', 'Stripe API'],
    features: [
      'Inventory and stock management',
      'Financial tracking and reporting',
      'Customer relationship management',
      'Automated invoicing and billing',
      'Real-time business analytics',
      'Multi-user access control'
    ],
    githubUrl: 'https://github.com/nrenx/trade-book-ledge',
    liveUrl: 'https://trade-book-ledge.vercel.app',
    imageUrl: '/assets/images/projects/saas-dashboard.jpg',
    category: 'web',
    status: 'completed',
    startDate: '2023-08',
    endDate: '2023-11',
  },
  {
    id: 'mobile-fitness-app',
    title: 'NBKRIST Student Portal',
    description: 'Academic portal for student management and services',
    longDescription: 'A comprehensive academic portal designed for NBKRIST students to access academic services, track progress, and manage their educational journey. Built with modern mobile-first design and cloud backend integration.',
    technologies: ['FlutterFlow', 'Firebase', 'Google Cloud', 'Stripe', 'Academic APIs'],
    features: [
      'Student profile and academic records',
      'Course registration and scheduling',
      'Grade tracking and progress analytics',
      'Fee payment and financial management',
      'Campus news and announcements',
      'Digital library access'
    ],
    githubUrl: 'https://github.com/nrenx/nbkrist-portal',
    liveUrl: 'https://nbkrist-portal.vercel.app',
    imageUrl: '/assets/images/projects/fitness-app.jpg',
    category: 'mobile',
    status: 'completed',
    startDate: '2023-05',
    endDate: '2023-09',
  },
  {
    id: 'automation-workflow',
    title: 'Business Process Automation',
    description: 'n8n workflow automation for business processes',
    longDescription: 'A comprehensive business process automation system built with n8n that streamlines operations, automates repetitive tasks, and integrates multiple business tools.',
    technologies: ['n8n', 'Telegram Bot API', 'Google Sheets API', 'Slack API', 'Webhook'],
    features: [
      'Automated lead processing',
      'Multi-channel notifications',
      'Data synchronization between platforms',
      'Custom Telegram bot for team updates',
      'Scheduled report generation',
      'Error handling and monitoring'
    ],
    githubUrl: 'https://github.com/nrenx/business-automation',
    liveUrl: null,
    imageUrl: '/assets/images/projects/automation.jpg',
    category: 'other',
    status: 'completed',
    startDate: '2023-12',
    endDate: '2024-02',
  },
  {
    id: 'ecommerce-platform',
    title: 'No-Code E-commerce Platform',
    description: 'Complete e-commerce solution using no-code tools',
    longDescription: 'A full-featured e-commerce platform built entirely with no-code tools, featuring product management, order processing, payment integration, and customer management.',
    technologies: ['Webflow', 'Airtable', 'Zapier', 'Stripe', 'Mailchimp'],
    features: [
      'Product catalog management',
      'Shopping cart and checkout',
      'Payment processing with Stripe',
      'Order management system',
      'Customer relationship management',
      'Email marketing automation'
    ],
    githubUrl: null,
    liveUrl: 'https://ecommerce-demo.webflow.io',
    imageUrl: '/assets/images/projects/ecommerce.jpg',
    category: 'web',
    status: 'completed',
    startDate: '2023-03',
    endDate: '2023-06',
  },
  {
    id: 'ai-chatbot',
    title: 'AI Customer Support Bot',
    description: 'Intelligent chatbot for customer support automation',
    longDescription: 'An AI-powered customer support chatbot that handles common inquiries, escalates complex issues, and provides 24/7 customer service using advanced prompt engineering.',
    technologies: ['OpenAI GPT-4', 'Dialogflow', 'Firebase', 'Telegram API', 'Webhook'],
    features: [
      'Natural language understanding',
      'Multi-language support',
      'Integration with existing CRM',
      'Escalation to human agents',
      'Analytics and performance tracking',
      'Custom training on business data'
    ],
    githubUrl: 'https://github.com/nrenx/ai-chatbot',
    liveUrl: 'https://t.me/customer_support_ai_bot',
    imageUrl: '/assets/images/projects/chatbot.jpg',
    category: 'other',
    status: 'completed',
    startDate: '2024-01',
    endDate: '2024-03',
  },
  {
    id: 'portfolio-website',
    title: 'Interactive Portfolio Website',
    description: 'Modern portfolio with macOS-style interface',
    longDescription: 'A modern, interactive portfolio website featuring a macOS-style interface, smooth animations, and responsive design. Built with React, Next.js, and Framer Motion.',
    technologies: ['React', 'Next.js', 'TypeScript', 'Tailwind CSS', 'Framer Motion'],
    features: [
      'macOS-style interface simulation',
      'Multi-language landing animation',
      'Dark/light theme switching',
      'Smooth scroll animations',
      'Responsive design',
      'Contact form with validation'
    ],
    githubUrl: 'https://github.com/nrenx/portfilio.git',
    liveUrl: 'https://nrenx.github.io/portfilio/',
    imageUrl: '/assets/images/projects/portfolio.jpg',
    category: 'web',
    status: 'completed',
    startDate: '2024-03',
    endDate: '2024-05',
  },
];

// Group projects by category
export const projectsByCategory = {
  web: projects.filter(p => p.category === 'web'),
  mobile: projects.filter(p => p.category === 'mobile'),
  automation: projects.filter(p => p.category === 'other'),
};

// Get featured projects
export const featuredProjects = projects.slice(0, 3);

// Get recent projects
export const recentProjects = projects
  .sort((a, b) => new Date(b.startDate).getTime() - new Date(a.startDate).getTime())
  .slice(0, 4);
